<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon, NGrid, NGridItem, NProgress } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  businessData?: Api.Monitor.BusinessStatsData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  businessData: null,
  loading: false
});

const router = useRouter();

// 计算任务统计
const taskStats = computed(() => {
  if (!props.businessData) {
    return {
      total: 0,
      completed: 0,
      running: 0,
      failed: 0,
      pending: 0,
      successRate: 0
    };
  }

  const tasks = props.businessData.strm_tasks;
  const failed = Math.max(0, tasks.total - tasks.completed - tasks.running);
  const pending = Math.max(0, tasks.total - tasks.completed - tasks.running - failed);

  // 处理成功率格式
  const rawSuccessRate = tasks.success_rate || 0;
  const successRate = rawSuccessRate <= 1 ? rawSuccessRate * 100 : rawSuccessRate;

  return {
    total: tasks.total,
    completed: tasks.completed,
    running: tasks.running,
    failed: failed,
    pending: pending,
    successRate: Math.min(successRate, 100)
  };
});

// 任务状态多层环形图 - 现代设计
const { domRef: taskRingRef, updateOptions: updateTaskRing } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: function(params) {
      return `
        <div style="display: flex; align-items: center; gap: 8px;">
          <div style="width: 8px; height: 8px; border-radius: 50%; background: ${params.color};"></div>
          <span style="font-weight: 600;">${params.name}</span>
          <span style="color: rgba(255,255,255,0.8);">${params.value}个</span>
          <span style="color: rgba(255,255,255,0.6); font-size: 12px;">(${params.percent}%)</span>
        </div>
      `;
    },
    backgroundColor: 'rgba(15, 23, 42, 0.95)',
    borderColor: 'rgba(148, 163, 184, 0.2)',
    borderWidth: 1,
    borderRadius: 12,
    padding: [12, 16],
    textStyle: {
      color: '#fff',
      fontSize: 13,
      fontWeight: 500,
      lineHeight: 1.4
    },
    confine: true,
    position: function (point, params, dom, rect, size) {
      // 智能定位，避免与图表重叠
      const tooltipWidth = 200;
      const tooltipHeight = 60;

      // 默认显示在图表右侧
      let x = size.viewSize[0] * 0.7;
      let y = size.viewSize[1] * 0.3;

      // 如果鼠标在右侧，显示在左侧
      if (point[0] > size.viewSize[0] * 0.5) {
        x = size.viewSize[0] * 0.05;
      }

      // 确保不超出边界
      x = Math.max(10, Math.min(x, size.viewSize[0] - tooltipWidth - 10));
      y = Math.max(10, Math.min(y, size.viewSize[1] - tooltipHeight - 10));

      return [x, y];
    },
    extraCssText: `
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      white-space: nowrap !important;
      min-width: 200px !important;
      width: auto !important;
      max-width: none !important;
      overflow: visible !important;
      z-index: 99999 !important;
      position: fixed !important;
      transition: all 0.2s ease;
      pointer-events: none !important;
    `
  },
  legend: {
    show: false
  },
  graphic: [
    {
      type: 'group',
      left: 'center',
      top: 'center',
      children: [
        {
          type: 'circle',
          shape: {
            cx: 0,
            cy: 0,
            r: 35
          },
          style: {
            fill: 'rgba(255, 255, 255, 0.95)',
            stroke: 'rgba(0, 0, 0, 0.1)',
            lineWidth: 1,
            shadowBlur: 8,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          }
        },
        {
          type: 'text',
          style: {
            text: '总任务',
            fontSize: 12,
            fontWeight: '500',
            fill: '#666',
            textAlign: 'center',
            y: -8
          }
        },
        {
          type: 'text',
          style: {
            text: '0',
            fontSize: 24,
            fontWeight: 'bold',
            fill: '#333',
            textAlign: 'center',
            y: 12
          }
        }
      ]
    }
  ],
  series: [
    // 主环形图 - 任务状态分布
    {
      name: '任务状态',
      type: 'pie',
      radius: ['50%', '75%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 3,
        shadowBlur: 12,
        shadowColor: 'rgba(0, 0, 0, 0.1)'
      },
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          scale: true,
          scaleSize: 8
        }
      },
      animationType: 'scale',
      animationEasing: 'elasticOut',
      animationDelay: function (idx: number) {
        return idx * 150;
      },
      data: [
        {
          value: 0,
          name: '已完成',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#52c41a' },
                { offset: 0.5, color: '#73d13d' },
                { offset: 1, color: '#95de64' }
              ]
            }
          }
        },
        {
          value: 0,
          name: '运行中',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#1890ff' },
                { offset: 0.5, color: '#40a9ff' },
                { offset: 1, color: '#69c0ff' }
              ]
            }
          }
        },
        {
          value: 0,
          name: '失败',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#ff4d4f' },
                { offset: 0.5, color: '#ff7875' },
                { offset: 1, color: '#ffa39e' }
              ]
            }
          }
        },
        {
          value: 0,
          name: '等待中',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#faad14' },
                { offset: 0.5, color: '#ffc53d' },
                { offset: 1, color: '#ffd666' }
              ]
            }
          }
        }
      ]
    }
  ]
}));

// 成功率多层环形进度图 - 现代设计
const { domRef: successCircleRef, updateOptions: updateSuccessCircle } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: function(params) {
      // 只为成功率部分显示工具提示
      if (params.name === '剩余') {
        return null; // 不显示工具提示
      }

      if (params.name === '成功率') {
        const successRate = params.percent;
        const failureRate = (100 - successRate).toFixed(1);

        let statusIcon = '📊';
        let statusText = '一般';
        let statusColor = '#faad14';

        if (successRate >= 95) {
          statusIcon = '🎉';
          statusText = '优秀';
          statusColor = '#52c41a';
        } else if (successRate >= 80) {
          statusIcon = '😊';
          statusText = '良好';
          statusColor = '#1890ff';
        } else if (successRate >= 60) {
          statusIcon = '😐';
          statusText = '一般';
          statusColor = '#faad14';
        } else {
          statusIcon = '😟';
          statusText = '较差';
          statusColor = '#ff4d4f';
        }

        return `
          <div style="text-align: center;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px;">
              <span style="font-size: 20px;">${statusIcon}</span>
              <span style="font-weight: 700; font-size: 16px;">任务成功率</span>
            </div>
            <div style="margin-bottom: 6px;">
              <span style="color: ${statusColor}; font-size: 24px; font-weight: 800;">${successRate}%</span>
              <span style="color: rgba(255,255,255,0.7); font-size: 12px; margin-left: 4px;">${statusText}</span>
            </div>
            <div style="font-size: 11px; color: rgba(255,255,255,0.6);">
              失败率: ${failureRate}%
            </div>
          </div>
        `;
      }

      return null;
    },
    backgroundColor: 'rgba(15, 23, 42, 0.96)',
    borderColor: 'rgba(148, 163, 184, 0.3)',
    borderWidth: 1,
    borderRadius: 16,
    padding: [20, 24],
    textStyle: {
      color: '#fff',
      fontSize: 13,
      fontWeight: 500,
      lineHeight: 1.5
    },
    confine: true,
    position: function (point, params, dom, rect, size) {
      // 智能定位，避免与图表重叠
      const tooltipWidth = 240;
      const tooltipHeight = 100;

      // 默认显示在图表右上角
      let x = size.viewSize[0] * 0.6;
      let y = size.viewSize[1] * 0.1;

      // 如果鼠标在右侧，显示在左侧
      if (point[0] > size.viewSize[0] * 0.6) {
        x = size.viewSize[0] * 0.05;
      }

      // 确保不超出边界
      x = Math.max(10, Math.min(x, size.viewSize[0] - tooltipWidth - 10));
      y = Math.max(10, Math.min(y, size.viewSize[1] - tooltipHeight - 10));

      return [x, y];
    },
    extraCssText: `
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 12px 24px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(24px);
      white-space: nowrap !important;
      min-width: 200px !important;
      width: auto !important;
      max-width: 240px !important;
      overflow: visible !important;
      z-index: 99999 !important;
      position: fixed !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      pointer-events: none !important;
    `
  },
  series: [
    // 外层装饰环
    {
      type: 'pie',
      radius: ['85%', '90%'],
      center: ['50%', '50%'],
      startAngle: 0,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: 100,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.1)' },
                { offset: 0.5, color: 'rgba(24, 144, 255, 0.2)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        }
      ],
      animationType: 'scale',
      animationEasing: 'cubicOut',
      animationDelay: 200
    },
    // 主进度环
    {
      type: 'pie',
      radius: ['65%', '80%'],
      center: ['50%', '50%'],
      startAngle: 90,
      clockwise: false,
      avoidLabelOverlap: false,
      hoverAnimation: false,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: taskStats.value.successRate,
          name: '成功率',
          itemStyle: {
            color: {
              type: 'conic',
              x: 0.5, y: 0.5,
              colorStops: [
                { offset: 0, color: '#ff4d4f' },
                { offset: 0.3, color: '#faad14' },
                { offset: 0.6, color: '#52c41a' },
                { offset: 1, color: '#1890ff' }
              ]
            },
            shadowBlur: 15,
            shadowColor: 'rgba(24, 144, 255, 0.4)',
            borderRadius: 8
          }
        },
        {
          value: 100 - taskStats.value.successRate,
          name: '剩余',
          itemStyle: {
            color: 'rgba(0, 0, 0, 0.05)',
            borderWidth: 0
          },
          emphasis: { disabled: true }
        }
      ],
      animationType: 'scale',
      animationEasing: 'elasticOut',
      animationDelay: 500
    },
    // 内层脉冲环
    {
      type: 'pie',
      radius: ['45%', '55%'],
      center: ['50%', '50%'],
      startAngle: 0,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      data: [
        {
          value: 100,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 0.8, color: 'rgba(24, 144, 255, 0.1)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0)' }
              ]
            }
          }
        }
      ],
      animationType: 'expansion',
      animationEasing: 'cubicOut',
      animationDelay: 800
    }
  ]
}));

// 成功率状态
const successRateStatus = computed(() => {
  const rate = taskStats.value.successRate;
  if (rate >= 95) return { type: 'success', color: '#52c41a', text: '优秀', icon: 'mdi:emoticon-excited' };
  if (rate >= 80) return { type: 'warning', color: '#faad14', text: '良好', icon: 'mdi:emoticon-happy' };
  if (rate >= 60) return { type: 'warning', color: '#faad14', text: '一般', icon: 'mdi:emoticon-neutral' };
  return { type: 'error', color: '#ff4d4f', text: '较差', icon: 'mdi:emoticon-sad' };
});

// 监听数据变化，更新图表
watch(() => props.businessData, () => {
  if (props.businessData) {
    const stats = taskStats.value;

    // 更新环形图
    updateTaskRing(prev => ({
      ...prev,
      graphic: [
        {
          type: 'group',
          left: 'center',
          top: 'center',
          children: [
            {
              type: 'circle',
              shape: {
                cx: 0,
                cy: 0,
                r: 35
              },
              style: {
                fill: 'rgba(255, 255, 255, 0.95)',
                stroke: 'rgba(0, 0, 0, 0.1)',
                lineWidth: 1,
                shadowBlur: 8,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            {
              type: 'text',
              style: {
                text: '总任务',
                fontSize: 12,
                fontWeight: '500',
                fill: '#666',
                textAlign: 'center',
                y: -8
              }
            },
            {
              type: 'text',
              style: {
                text: stats.total.toString(),
                fontSize: 24,
                fontWeight: 'bold',
                fill: '#333',
                textAlign: 'center',
                y: 12
              }
            }
          ]
        }
      ],
      series: [
        {
          ...prev.series[0],
          data: [
            {
              value: stats.completed,
              name: '已完成',
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 1,
                  colorStops: [
                    { offset: 0, color: '#52c41a' },
                    { offset: 0.5, color: '#73d13d' },
                    { offset: 1, color: '#95de64' }
                  ]
                }
              }
            },
            {
              value: stats.running,
              name: '运行中',
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 1,
                  colorStops: [
                    { offset: 0, color: '#1890ff' },
                    { offset: 0.5, color: '#40a9ff' },
                    { offset: 1, color: '#69c0ff' }
                  ]
                }
              }
            },
            {
              value: stats.failed,
              name: '失败',
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 1,
                  colorStops: [
                    { offset: 0, color: '#ff4d4f' },
                    { offset: 0.5, color: '#ff7875' },
                    { offset: 1, color: '#ffa39e' }
                  ]
                }
              }
            },
            {
              value: stats.pending,
              name: '等待中',
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 1,
                  colorStops: [
                    { offset: 0, color: '#faad14' },
                    { offset: 0.5, color: '#ffc53d' },
                    { offset: 1, color: '#ffd666' }
                  ]
                }
              }
            }
          ].filter(item => item.value > 0)
        }
      ]
    }));

    // 更新多层环形进度图
    const getSuccessRateColors = (rate: number) => {
      if (rate >= 80) return { primary: '#52c41a', secondary: '#73d13d', shadow: '82, 196, 26' };
      if (rate >= 60) return { primary: '#faad14', secondary: '#ffc53d', shadow: '250, 173, 20' };
      return { primary: '#ff4d4f', secondary: '#ff7875', shadow: '255, 77, 79' };
    };

    const colors = getSuccessRateColors(stats.successRate);

    updateSuccessCircle(prev => ({
      ...prev,
      series: [
        // 外层装饰环
        {
          ...prev.series[0],
          data: [{
            value: 100,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: `rgba(${colors.shadow}, 0.1)` },
                  { offset: 0.5, color: `rgba(${colors.shadow}, 0.2)` },
                  { offset: 1, color: `rgba(${colors.shadow}, 0.1)` }
                ]
              }
            }
          }]
        },
        // 主进度环
        {
          ...prev.series[1],
          data: [
            {
              value: stats.successRate,
              name: '成功率',
              itemStyle: {
                color: {
                  type: 'conic',
                  x: 0.5, y: 0.5,
                  colorStops: [
                    { offset: 0, color: '#ff4d4f' },
                    { offset: 0.3, color: '#faad14' },
                    { offset: 0.6, color: '#52c41a' },
                    { offset: 1, color: colors.primary }
                  ]
                },
                shadowBlur: 15,
                shadowColor: `rgba(${colors.shadow}, 0.4)`,
                borderRadius: 8
              }
            },
            {
              value: 100 - stats.successRate,
              name: '剩余',
              itemStyle: {
                color: 'rgba(0, 0, 0, 0.05)',
                borderWidth: 0
              },
              emphasis: { disabled: true }
            }
          ]
        },
        // 内层脉冲环
        {
          ...prev.series[2],
          data: [{
            value: 100,
            itemStyle: {
              color: {
                type: 'radial',
                x: 0.5, y: 0.5, r: 0.5,
                colorStops: [
                  { offset: 0, color: `rgba(${colors.shadow}, 0.3)` },
                  { offset: 0.8, color: `rgba(${colors.shadow}, 0.1)` },
                  { offset: 1, color: `rgba(${colors.shadow}, 0)` }
                ]
              }
            }
          }]
        }
      ]
    }));
  }
}, { immediate: true });

function goToTaskDetail() {
  router.push('/monitor/tasks');
}
</script>

<template>
  <NCard class="modern-task-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="icon-wrapper">
            <Icon icon="mdi:chart-donut" class="text-2xl" />
          </div>
          <div>
            <span class="text-lg font-semibold text-gray-800">任务执行概览</span>
            <div class="text-sm text-gray-500">实时监控任务状态分布</div>
          </div>
        </div>
        <NButton text @click="goToTaskDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div class="card-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：任务状态分布 + 成功率 -->
        <div class="left-section">
          <div class="chart-header">
            <h4 class="chart-title">任务状态分布</h4>
          </div>

          <!-- 环形图和成功率组合 -->
          <div class="chart-combined">
            <div ref="taskRingRef" class="chart-canvas ring-chart"></div>

            <!-- 成功率显示在环形图右侧 -->
            <div class="success-rate-section">


              <!-- 成功率主要显示区域 -->
              <div class="success-rate-main">
                <div class="rate-chart-container">
                  <div ref="successCircleRef" class="chart-canvas success-circle"></div>

                  <!-- 中心数字显示 -->
                  <div class="rate-center-display">
                    <div class="rate-title">成功率</div>
                    <div class="rate-value-large">
                      <span class="rate-number-large">{{ Math.round(taskStats.successRate) }}</span>
                      <span class="rate-unit-large">%</span>
                    </div>
                    <div class="rate-status-mini">
                      <Icon :icon="successRateStatus.icon" class="status-icon-mini" :style="{ color: successRateStatus.color }" />
                      <span class="status-text-mini" :style="{ color: successRateStatus.color }">{{ successRateStatus.text }}</span>
                    </div>
                  </div>

                  <!-- 动态背景效果 -->
                  <div class="rate-background-effects">
                    <div class="pulse-ring" :class="successRateStatus.type"></div>
                    <div class="glow-effect" :style="{ backgroundColor: successRateStatus.color + '20' }"></div>
                  </div>
                </div>


              </div>
            </div>
          </div>


        </div>

        <!-- 右侧：统计卡片 -->
        <div class="right-section">
          <div class="chart-header">
            <h4 class="chart-title">统计概览</h4>
          </div>

          <div class="stats-grid">
            <!-- 总任务数卡片 -->
            <div class="stat-item total-tasks">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon-wrapper">
                <Icon icon="mdi:format-list-bulleted" class="stat-icon" />
              </div>
              <div class="stat-header">
                <div class="stat-trend">
                  <Icon icon="mdi:trending-up" class="trend-icon" />
                </div>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ taskStats.total }}</div>
              </div>
              <div class="stat-footer">
                <div class="stat-label">总任务数</div>
              </div>
            </div>

            <!-- 已完成卡片 -->
            <div class="stat-item completed-tasks">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon-wrapper">
                <Icon icon="mdi:check-circle" class="stat-icon" />
              </div>
              <div class="stat-header">
                <div class="stat-percentage">
                  {{ taskStats.total > 0 ? Math.round((taskStats.completed / taskStats.total) * 100) : 0 }}%
                </div>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ taskStats.completed }}</div>
              </div>
              <div class="stat-footer">
                <div class="stat-label">已完成</div>
              </div>
            </div>

            <!-- 运行中卡片 -->
            <div class="stat-item running-tasks">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon-wrapper">
                <Icon icon="mdi:play-circle" class="stat-icon" />
              </div>
              <div class="stat-header">
                <div class="stat-indicator" v-if="taskStats.running > 0">
                  <div class="pulse-dot"></div>
                  <span class="indicator-text">实时</span>
                </div>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ taskStats.running }}</div>
              </div>
              <div class="stat-footer">
                <div class="stat-label">运行中</div>
              </div>
            </div>

            <!-- 失败卡片 -->
            <div class="stat-item failed-tasks">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon-wrapper">
                <Icon icon="mdi:alert-circle" class="stat-icon" />
              </div>
              <div class="stat-header">
                <div class="stat-alert" v-if="taskStats.failed > 0">
                  <Icon icon="mdi:alert" class="alert-icon" />
                  <span class="alert-text">注意</span>
                </div>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ taskStats.failed }}</div>
              </div>
              <div class="stat-footer">
                <div class="stat-label">失败</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-task-overview-card {
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: visible;
  position: relative;
}

.modern-task-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 300% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.modern-task-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.detail-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.card-content {
  padding: 0;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  overflow: visible;
}

.left-section,
.right-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  overflow: visible;
}

.left-section:hover,
.right-section:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.chart-combined {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.ring-chart {
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  overflow: visible;
  z-index: 1;
}

.success-rate-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}



.success-rate-main {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.rate-chart-container {
  position: relative;
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  z-index: 1;
}

.success-circle {
  width: 180px;
  height: 180px;
  position: relative;
  z-index: 2;
}

.rate-center-display {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  text-align: center;
  pointer-events: none;
}

.rate-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 4px;
  text-align: center;
}

.rate-value-large {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
  margin-bottom: 4px;
}

.rate-number-large {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  animation: numberPulse 2s ease-in-out infinite;
}

.rate-unit-large {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.rate-status-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.status-icon-mini {
  font-size: 12px;
}

.status-text-mini {
  font-size: 10px;
  font-weight: 600;
}

.rate-background-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  border-radius: 50%;
  animation: pulseRing 3s ease-in-out infinite;
}

.pulse-ring.success {
  border: 2px solid rgba(82, 196, 26, 0.3);
}

.pulse-ring.warning {
  border: 2px solid rgba(250, 173, 20, 0.3);
}

.pulse-ring.error {
  border: 2px solid rgba(255, 77, 79, 0.3);
}

.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  border-radius: 50%;
  filter: blur(20px);
  animation: glowPulse 4s ease-in-out infinite;
}



@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes patternFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

@keyframes numberGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
  }
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  50%, 100% {
    transform: translateX(100%);
  }
}



.success-rate-display {
  text-align: center;
  margin-bottom: 16px;
}

.rate-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.rate-number {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.rate-unit {
  font-size: 18px;
  font-weight: 600;
  color: #6b7280;
}

.rate-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-icon {
  font-size: 18px;
}

.status-text {
  font-size: 14px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 14px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.stat-item.total-tasks::before {
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #a855f7);
}

.stat-item.completed-tasks::before {
  background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
}

.stat-item.running-tasks::before {
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
}

.stat-item.failed-tasks::before {
  background: linear-gradient(90deg, #ef4444, #f87171, #fca5a5);
}

.stat-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-item:hover::before {
  height: 6px;
}

.stat-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
  pointer-events: none;
}

.stat-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  background-size: 60px 60px;
  animation: patternFloat 20s ease-in-out infinite;
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  overflow: hidden;
  top: 50%;
  left: 14px;
  transform: translateY(-50%);
}

.stat-item.total-tasks .stat-icon-wrapper {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.stat-item.completed-tasks .stat-icon-wrapper {
  background: linear-gradient(135deg, #10b981, #34d399);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.stat-item.running-tasks .stat-icon-wrapper {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.stat-item.failed-tasks .stat-icon-wrapper {
  background: linear-gradient(135deg, #ef4444, #f87171);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.stat-icon {
  font-size: 18px;
  color: white;
  z-index: 2;
}

.stat-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon-wrapper::before {
  transform: scale(1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  z-index: 2;
}

.stat-value {
  font-size: 30px;
  font-weight: 800;
  background: linear-gradient(135deg, #1f2937, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 1px;
  animation: numberGlow 3s ease-in-out infinite;
}

.stat-label {
  font-size: 11px;
  color: #374151;
  font-weight: 700;
  margin-top: 0;
  line-height: 1.2;
  white-space: nowrap;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid rgba(203, 213, 225, 0.4);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: labelGlow 4s ease-in-out infinite;
  min-width: 60px;
}

.stat-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.stat-item:hover .stat-label::before {
  left: 100%;
}

.stat-item:hover .stat-label {
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 不同类型标签的特定样式 */
.stat-item.total-tasks .stat-label {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #4f46e5;
}

.stat-item.completed-tasks .stat-label {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.05) 100%);
  border-color: rgba(16, 185, 129, 0.2);
  color: #059669;
}

.stat-item.running-tasks .stat-label {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(96, 165, 250, 0.05) 100%);
  border-color: rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.stat-item.failed-tasks .stat-label {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.05) 100%);
  border-color: rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.stat-header {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.stat-footer {
  position: absolute;
  bottom: 8px;
  right: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-badge.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.stat-badge.running {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.stat-badge.failed {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.stat-badge:not(.success):not(.running):not(.failed) {
  background: rgba(99, 102, 241, 0.1);
  color: #4f46e5;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.stat-progress {
  width: 100%;
}

.progress-track {
  height: 4px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  width: 80px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
  border-radius: 2px;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShine 2s ease-in-out infinite;
}

.stat-percentage {
  font-size: 11px;
  font-weight: 600;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  min-width: 40px;
  text-align: center;
}

.stat-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  min-width: 40px;
  justify-content: center;
}

.pulse-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
}

.indicator-text {
  font-size: 9px;
  font-weight: 600;
  color: #3b82f6;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  min-width: 40px;
}

.trend-icon {
  font-size: 14px;
  color: #10b981;
}

.stat-alert {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  min-width: 40px;
}

.alert-icon {
  font-size: 12px;
  color: #ef4444;
  animation: bounce 2s infinite;
}

.alert-text {
  font-size: 9px;
  font-weight: 600;
  color: #ef4444;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes labelGlow {
  0%, 100% {
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.08),
      0 2px 4px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow:
      0 6px 16px rgba(0, 0, 0, 0.12),
      0 3px 6px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-combined {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .success-rate-section {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    height: 80px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 10px;
  }

  .stat-icon-wrapper {
    width: 32px;
    height: 32px;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
  }

  .stat-icon {
    font-size: 16px;
  }

  .stat-header {
    top: 6px;
    right: 6px;
  }

  .stat-footer {
    bottom: 6px;
    right: 35px;
  }

  .stat-label {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 6px;
    min-width: 45px;
  }

  .ring-chart {
    width: 180px;
    height: 180px;
  }

  .success-circle {
    width: 100px;
    height: 100px;
  }
}

/* 全局工具提示样式美化 */
:deep(.echarts-tooltip) {
  white-space: nowrap !important;
  min-width: 180px !important;
  width: auto !important;
  max-width: none !important;
  overflow: visible !important;
  z-index: 9999 !important;
  border-radius: 12px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

/* ECharts 工具提示容器美化 */
:global(.echarts-tooltip) {
  white-space: nowrap !important;
  min-width: 180px !important;
  width: auto !important;
  max-width: none !important;
  overflow: visible !important;
  z-index: 9999 !important;
  border-radius: 12px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

/* 工具提示动画效果 */
:deep(.echarts-tooltip),
:global(.echarts-tooltip) {
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
