<template>
  <div class="test-tooltip-page">
    <h2>测试成功率图表 Tooltip</h2>
    <p>鼠标悬浮在成功率图表上，检查 tooltip 是否与图表重叠</p>
    
    <div class="test-container">
      <ModernTaskOverviewCard :business-data="testData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ModernTaskOverviewCard from './monitor/dashboard/components/modern-task-overview-card.vue';

const testData = ref({
  total: 170,
  completed: 41,
  running: 0,
  failed: 129,
  pending: 0,
  successRate: 24.12
});
</script>

<style scoped>
.test-tooltip-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  margin-top: 20px;
  height: 500px;
}
</style>
